{"name": "vocalpipe", "version": "1.0.0", "description": "Advanced multilingual voice and text AI bot with dual language support", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "start": "node dist/index.js", "watch": "tsc --watch", "dev:watch": "nodemon --exec ts-node src/index.ts", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts"}, "keywords": ["telegram", "bot", "ai", "voice", "text", "multilingual", "speech-to-text", "text-to-speech", "translation", "openai", "<PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON>", "license": "MIT", "dependencies": {"telegraf": "^4.16.3", "openai": "^4.103.0", "fluent-ffmpeg": "^2.1.2", "node-fetch": "^3.3.2", "uuid": "^9.0.1", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^20.11.24", "@types/fluent-ffmpeg": "^2.1.24", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "eslint": "^8.57.0", "jest": "^29.7.0", "@types/jest": "^29.5.12", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.4.2", "nodemon": "^3.1.0", "prettier": "^3.2.5", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0"}}